{"name": "linkwarden", "version": "v2.10.0", "main": "index.js", "repository": "https://github.com/linkwarden/linkwarden.git", "author": "Daniel31X13 <<EMAIL>>", "license": "AGPL-3.0", "private": true, "prisma": {"seed": "node ./prisma/seed.js"}, "scripts": {"dev": "concurrently -k -<PERSON> \"next dev {@}\" \"yarn worker:dev\" --", "worker:dev": "nodemon --skip-project scripts/worker.ts", "worker:prod": "ts-node --transpile-only --skip-project scripts/worker.ts", "start": "concurrently -P \"next start {@}\" \"yarn worker:prod\" --", "build": "next build", "lint": "next lint", "e2e": "playwright test e2e", "format": "prettier --write \"**/*.{ts,tsx,js,json,md}\""}, "dependencies": {"@ai-sdk/anthropic": "1.1.5", "@ai-sdk/azure": "1.1.5", "@ai-sdk/openai": "1.1.5", "@atlaskit/tree": "^8.8.7", "@auth/prisma-adapter": "^1.0.1", "@aws-sdk/client-s3": "^3.379.1", "@headlessui/react": "^1.7.15", "@mozilla/readability": "^0.4.4", "@openrouter/ai-sdk-provider": "^0.4.3", "@phosphor-icons/core": "^2.1.1", "@phosphor-icons/react": "^2.1.7", "@prisma/client": "^5.21.1", "@stripe/stripe-js": "^1.54.1", "@tanstack/react-query": "^5.51.15", "@tanstack/react-query-devtools": "^5.51.15", "@types/crypto-js": "^4.1.1", "@types/formidable": "^3.4.5", "@types/node": "^20.10.4", "@types/nodemailer": "^6.4.8", "@types/react": "18.2.14", "@types/react-dom": "18.2.7", "@types/rss": "^0.0.32", "ai": "^4.1.10", "axios": "^1.5.1", "bcrypt": "^5.1.0", "bootstrap-icons": "^1.11.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "colorjs.io": "^0.5.2", "colorthief": "^2.4.0", "concurrently": "^8.2.2", "crypto-js": "^4.2.0", "csstype": "^3.1.2", "dompurify": "^3.0.6", "dotenv": "^16.3.1", "eslint": "8.46.0", "eslint-config-next": "13.4.9", "formidable": "^3.5.1", "framer-motion": "^10.16.4", "fuse.js": "^7.0.0", "handlebars": "^4.7.8", "himalaya": "^1.1.0", "i18next": "^23.11.5", "jimp": "^0.22.10", "jsdom": "^22.1.0", "jszip": "^3.10.1", "lottie-web": "^5.12.2", "meilisearch": "^0.48.2", "micro": "^10.0.1", "next": "13.4.12", "next-auth": "^4.22.1", "next-i18next": "^15.3.0", "node-fetch": "^2.7.0", "nodemailer": "^6.9.3", "ollama-ai-provider": "^1.2.0", "playwright": "^1.45.0", "react": "18.2.0", "react-colorful": "^5.6.1", "react-dom": "18.2.0", "react-hot-toast": "^2.4.1", "react-i18next": "^14.1.2", "react-image-file-resizer": "^0.4.8", "react-intersection-observer": "^9.13.0", "react-masonry-css": "^1.0.16", "react-select": "^5.7.4", "react-spinners": "^0.14.1", "react-window": "^1.8.10", "rss": "^1.2.2", "rss-parser": "^3.13.0", "socks-proxy-agent": "^8.0.2", "stripe": "^12.13.0", "tailwind-merge": "^2.3.0", "vaul": "^1.1.1", "zod": "^3.23.8", "zustand": "^4.3.8"}, "devDependencies": {"@playwright/test": "^1.45.0", "@types/bcrypt": "^5.0.0", "@types/dompurify": "^3.0.4", "@types/jsdom": "^21.1.3", "@types/node-fetch": "^2.6.10", "@types/react-window": "^1.8.8", "@types/shelljs": "^0.8.15", "autoprefixer": "^10.4.14", "daisyui": "^4.4.2", "nodemon": "^3.0.2", "postcss": "^8.4.26", "prettier": "3.1.1", "prisma": "^5.21.1", "tailwindcss": "^3.4.10", "ts-node": "^10.9.2", "typescript": "4.9.4"}}