# Linkwarden - Debian Cloud Deployment Guide

Complete step-by-step guide for deploying Linkwarden on a Debian-based cloud instance (Ubuntu 22.04/24.04, Debian 11/12).

---

## Table of Contents
1. [Server Prerequisites](#server-prerequisites)
2. [Initial Server Setup](#initial-server-setup)
3. [Install Dependencies](#install-dependencies)
4. [Application Setup](#application-setup)
5. [Database Configuration](#database-configuration)
6. [Systemd Service Setup](#systemd-service-setup)
7. [Reverse Proxy Configuration](#reverse-proxy-configuration)
8. [SSL Certificate Setup](#ssl-certificate-setup)
9. [Firewall Configuration](#firewall-configuration)
10. [Monitoring and Maintenance](#monitoring-and-maintenance)

---

## Server Prerequisites

### Minimum Requirements:
- **CPU**: 2 cores
- **RAM**: 4 GB
- **Storage**: 20 GB SSD
- **OS**: Ubuntu 22.04/24.04 LTS or Debian 11/12
- **Network**: Public IP address
- **Domain**: Optional but recommended (e.g., bookmarks.yourdomain.com)

### Recommended for Production:
- **CPU**: 4 cores
- **RAM**: 8 GB
- **Storage**: 50 GB SSD
- **Backup**: Automated daily backups

---

## Initial Server Setup

### 1. Update System

```bash
# Update package lists
sudo apt update && sudo apt upgrade -y

# Install basic utilities
sudo apt install -y curl wget git vim ufw fail2ban
```

### 2. Create Application User

```bash
# Create a dedicated user for the application
sudo adduser --system --group --home /opt/linkwarden linkwarden

# Add to necessary groups
sudo usermod -aG sudo linkwarden  # Optional: if you need sudo access
```

### 3. Configure Firewall (UFW)

```bash
# Enable UFW
sudo ufw enable

# Allow SSH (IMPORTANT: Do this first!)
sudo ufw allow 22/tcp

# Allow HTTP and HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Check status
sudo ufw status
```

---

## Install Dependencies

### 1. Install Node.js 18.x LTS

```bash
# Add NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -

# Install Node.js
sudo apt install -y nodejs

# Verify installation
node --version  # Should show v18.x.x
npm --version
```

### 2. Install Yarn

```bash
# Install Yarn globally
sudo npm install -g yarn

# Verify installation
yarn --version
```

### 3. Install PostgreSQL 16

```bash
# Add PostgreSQL repository
sudo sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list'
wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo apt-key add -

# Update and install
sudo apt update
sudo apt install -y postgresql-16 postgresql-contrib-16

# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Verify installation
sudo systemctl status postgresql
```

### 4. Install Meilisearch

```bash
# Download and install Meilisearch
curl -L https://install.meilisearch.com | sh

# Move to system binary location
sudo mv ./meilisearch /usr/local/bin/

# Verify installation
meilisearch --version
```

### 5. Install Nginx (Reverse Proxy)

```bash
# Install Nginx
sudo apt install -y nginx

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Verify installation
sudo systemctl status nginx
```

---

## Application Setup

### 1. Clone Repository

```bash
# Switch to linkwarden user
sudo su - linkwarden

# Clone the repository
cd /opt/linkwarden
git clone https://github.com/linkwarden/linkwarden.git app
cd app

# Checkout latest stable release (optional)
git checkout v2.10.0  # Replace with latest version
```

### 2. Install Application Dependencies

```bash
# Install Node.js dependencies
yarn install --production=false

# This may take 5-10 minutes
```

### 3. Configure Environment Variables

```bash
# Create .env file
nano /opt/linkwarden/app/.env
```

Add the following configuration:

```env
# Database Configuration
DATABASE_URL=postgresql://linkwarden:STRONG_DB_PASSWORD@localhost:5432/linkwarden

# NextAuth Configuration (Generate a random 32+ character string)
NEXTAUTH_SECRET=GENERATE_A_RANDOM_SECRET_HERE_MIN_32_CHARS
NEXTAUTH_URL=https://bookmarks.yourdomain.com

# Meilisearch Configuration
MEILI_HOST=http://localhost:7700
MEILI_MASTER_KEY=GENERATE_A_RANDOM_MEILI_KEY_HERE

# Storage Configuration
STORAGE_FOLDER=/opt/linkwarden/data

# Application Configuration
NEXT_PUBLIC_DISABLE_REGISTRATION=false
NEXT_PUBLIC_CREDENTIALS_ENABLED=true

# Worker Configuration
ARCHIVE_TAKE_COUNT=5
INDEX_TAKE_COUNT=50
ARCHIVE_SCRIPT_INTERVAL=10
NEXT_PUBLIC_RSS_POLLING_INTERVAL_MINUTES=60

# Production Mode
NODE_ENV=production

# Optional: Email Configuration (for user verification)
# EMAIL_FROM=<EMAIL>
# EMAIL_SERVER=smtp://username:<EMAIL>:587

# Optional: Disable registration after first user
# NEXT_PUBLIC_DISABLE_REGISTRATION=true
```

**Generate secure secrets:**
```bash
# Generate NEXTAUTH_SECRET
openssl rand -base64 32

# Generate MEILI_MASTER_KEY
openssl rand -base64 32
```

### 4. Create Data Directory

```bash
# Create data directory
sudo mkdir -p /opt/linkwarden/data
sudo chown -R linkwarden:linkwarden /opt/linkwarden/data
sudo chmod 755 /opt/linkwarden/data
```

---

## Database Configuration

### 1. Create PostgreSQL Database and User

```bash
# Switch to postgres user
sudo -u postgres psql

# In PostgreSQL prompt, run:
CREATE DATABASE linkwarden;
CREATE USER linkwarden WITH ENCRYPTED PASSWORD 'STRONG_DB_PASSWORD';
GRANT ALL PRIVILEGES ON DATABASE linkwarden TO linkwarden;
\q
```

### 2. Run Database Migrations

```bash
# Switch to linkwarden user
sudo su - linkwarden
cd /opt/linkwarden/app

# Run Prisma migrations
npx prisma migrate deploy

# Generate Prisma client
npx prisma generate
```

### 3. Build Application

```bash
# Build Next.js application
yarn build

# This may take 5-10 minutes
```

---

## Systemd Service Setup

### 1. Create Meilisearch Service

```bash
sudo nano /etc/systemd/system/meilisearch.service
```

Add the following:

```ini
[Unit]
Description=Meilisearch Search Engine
After=network.target

[Service]
Type=simple
User=linkwarden
Group=linkwarden
WorkingDirectory=/opt/linkwarden
Environment="MEILI_MASTER_KEY=YOUR_MEILI_MASTER_KEY_HERE"
Environment="MEILI_DB_PATH=/opt/linkwarden/meili_data"
Environment="MEILI_HTTP_ADDR=127.0.0.1:7700"
ExecStart=/usr/local/bin/meilisearch
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 2. Create Linkwarden Application Service

```bash
sudo nano /etc/systemd/system/linkwarden.service
```

Add the following:

```ini
[Unit]
Description=Linkwarden Bookmark Manager
After=network.target postgresql.service meilisearch.service
Requires=postgresql.service meilisearch.service

[Service]
Type=simple
User=linkwarden
Group=linkwarden
WorkingDirectory=/opt/linkwarden/app
Environment="NODE_ENV=production"
Environment="PORT=3000"
EnvironmentFile=/opt/linkwarden/app/.env
ExecStart=/usr/bin/yarn start
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

### 3. Create Linkwarden Worker Service

```bash
sudo nano /etc/systemd/system/linkwarden-worker.service
```

Add the following:

```ini
[Unit]
Description=Linkwarden Background Worker
After=network.target postgresql.service meilisearch.service linkwarden.service
Requires=postgresql.service meilisearch.service

[Service]
Type=simple
User=linkwarden
Group=linkwarden
WorkingDirectory=/opt/linkwarden/app
Environment="NODE_ENV=production"
EnvironmentFile=/opt/linkwarden/app/.env
ExecStart=/usr/bin/yarn worker:prod
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

### 4. Enable and Start Services

```bash
# Reload systemd
sudo systemctl daemon-reload

# Enable services to start on boot
sudo systemctl enable meilisearch
sudo systemctl enable linkwarden
sudo systemctl enable linkwarden-worker

# Start services
sudo systemctl start meilisearch
sudo systemctl start linkwarden
sudo systemctl start linkwarden-worker

# Check status
sudo systemctl status meilisearch
sudo systemctl status linkwarden
sudo systemctl status linkwarden-worker
```

---

## Reverse Proxy Configuration

### Configure Nginx

```bash
sudo nano /etc/nginx/sites-available/linkwarden
```

Add the following configuration:

```nginx
# HTTP - Redirect to HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name bookmarks.yourdomain.com;

    # Redirect all HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name bookmarks.yourdomain.com;

    # SSL Configuration (will be configured by Certbot)
    # ssl_certificate /etc/letsencrypt/live/bookmarks.yourdomain.com/fullchain.pem;
    # ssl_certificate_key /etc/letsencrypt/live/bookmarks.yourdomain.com/privkey.pem;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # Client body size (for file uploads)
    client_max_body_size 100M;

    # Proxy settings
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Logging
    access_log /var/log/nginx/linkwarden_access.log;
    error_log /var/log/nginx/linkwarden_error.log;
}
```

Enable the site:

```bash
# Create symbolic link
sudo ln -s /etc/nginx/sites-available/linkwarden /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
```

---

## SSL Certificate Setup

### Install Certbot and Obtain SSL Certificate

```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Obtain SSL certificate (replace with your domain)
sudo certbot --nginx -d bookmarks.yourdomain.com

# Follow the prompts:
# - Enter email address
# - Agree to terms
# - Choose whether to redirect HTTP to HTTPS (recommended: Yes)

# Test automatic renewal
sudo certbot renew --dry-run
```

The certificate will auto-renew. Certbot creates a cron job automatically.

---

## Firewall Configuration

```bash
# Allow Nginx Full (HTTP + HTTPS)
sudo ufw allow 'Nginx Full'

# Remove individual HTTP/HTTPS rules if added earlier
sudo ufw delete allow 80/tcp
sudo ufw delete allow 443/tcp

# Deny direct access to application port
sudo ufw deny 3000/tcp

# Deny direct access to database
sudo ufw deny 5432/tcp

# Deny direct access to Meilisearch
sudo ufw deny 7700/tcp

# Reload firewall
sudo ufw reload

# Check status
sudo ufw status numbered
```

---

## Monitoring and Maintenance

### View Logs

```bash
# Application logs
sudo journalctl -u linkwarden -f

# Worker logs
sudo journalctl -u linkwarden-worker -f

# Meilisearch logs
sudo journalctl -u meilisearch -f

# Nginx logs
sudo tail -f /var/log/nginx/linkwarden_access.log
sudo tail -f /var/log/nginx/linkwarden_error.log
```

### Restart Services

```bash
# Restart application
sudo systemctl restart linkwarden

# Restart worker
sudo systemctl restart linkwarden-worker

# Restart Meilisearch
sudo systemctl restart meilisearch

# Restart Nginx
sudo systemctl restart nginx
```

### Database Backup

```bash
# Create backup script
sudo nano /opt/linkwarden/backup.sh
```

Add:

```bash
#!/bin/bash
BACKUP_DIR="/opt/linkwarden/backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Backup PostgreSQL database
sudo -u postgres pg_dump linkwarden > $BACKUP_DIR/linkwarden_db_$DATE.sql

# Backup data directory
tar -czf $BACKUP_DIR/linkwarden_data_$DATE.tar.gz /opt/linkwarden/data

# Keep only last 7 days of backups
find $BACKUP_DIR -name "linkwarden_*" -mtime +7 -delete

echo "Backup completed: $DATE"
```

Make executable and add to cron:

```bash
sudo chmod +x /opt/linkwarden/backup.sh

# Add to crontab (daily at 2 AM)
sudo crontab -e
# Add line:
0 2 * * * /opt/linkwarden/backup.sh >> /var/log/linkwarden_backup.log 2>&1
```

### Update Application

```bash
# Switch to linkwarden user
sudo su - linkwarden
cd /opt/linkwarden/app

# Pull latest changes
git fetch --all
git checkout v2.11.0  # Replace with desired version

# Install dependencies
yarn install

# Run migrations
npx prisma migrate deploy

# Build application
yarn build

# Exit linkwarden user
exit

# Restart services
sudo systemctl restart linkwarden
sudo systemctl restart linkwarden-worker
```

### Health Checks

```bash
# Check if application is responding
curl -I http://localhost:3000

# Check if Meilisearch is responding
curl http://localhost:7700/health

# Check database connection
sudo -u postgres psql -d linkwarden -c "SELECT version();"
```

---

## Security Best Practices

1. **Keep system updated:**
   ```bash
   sudo apt update && sudo apt upgrade -y
   ```

2. **Configure fail2ban for SSH protection:**
   ```bash
   sudo systemctl enable fail2ban
   sudo systemctl start fail2ban
   ```

3. **Use strong passwords** for database and application secrets

4. **Regular backups** - Automate daily backups

5. **Monitor logs** regularly for suspicious activity

6. **Disable root SSH login:**
   ```bash
   sudo nano /etc/ssh/sshd_config
   # Set: PermitRootLogin no
   sudo systemctl restart sshd
   ```

7. **Use SSH keys** instead of passwords

8. **Keep application updated** to latest stable version

---

## Troubleshooting

### Application won't start
```bash
# Check logs
sudo journalctl -u linkwarden -n 50

# Check if port is in use
sudo lsof -i :3000

# Verify environment variables
sudo -u linkwarden cat /opt/linkwarden/app/.env
```

### Database connection errors
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Test database connection
sudo -u postgres psql -d linkwarden -c "SELECT 1;"

# Check DATABASE_URL in .env
```

### SSL certificate issues
```bash
# Renew certificate manually
sudo certbot renew

# Check certificate status
sudo certbot certificates
```

---

## Post-Deployment Checklist

- [ ] Application accessible via HTTPS
- [ ] SSL certificate valid and auto-renewing
- [ ] Firewall configured correctly
- [ ] All services start on boot
- [ ] Backups configured and tested
- [ ] Logs rotating properly
- [ ] First user account created
- [ ] Email notifications working (if configured)
- [ ] Search functionality working
- [ ] Bookmark archival working (PDF, screenshot, etc.)
- [ ] Monitoring/alerting configured (optional)

---

## Support and Resources

- **Documentation**: https://docs.linkwarden.app
- **GitHub Issues**: https://github.com/linkwarden/linkwarden/issues
- **Discord Community**: https://discord.com/invite/CtuYV47nuJ
- **Security Issues**: <EMAIL>

---

**Congratulations!** Your Linkwarden instance is now deployed and ready for production use.

