name: Check pull request source branch
on:
  pull_request_target:
    types:
      - opened
      - reopened
      - synchronize
      - edited
jobs:
  check-branches:
    runs-on: ubuntu-latest
    steps:
      - name: Check branches
        run: |
          if [ ${{ github.head_ref }} != "dev" ] && [ ${{ github.base_ref }} == "main" ]; then
            echo "Merge requests to main branch are only allowed from dev branch. Please rebase your changes to dev branch."
            exit 1
          fi
