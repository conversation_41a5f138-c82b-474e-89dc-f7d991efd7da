# Linkwarden - Quick Start Guide

## 🚀 Fastest Way to Get Started

### Option 1: Automated Setup (Recommended)

Run the automated setup script that installs everything and starts the application:

```bash
cd ~/Documents/NORMANS/NORMANS/Bookmark_Manager-main
./setup-and-run.sh
```

This script will:
- ✓ Install <PERSON>dman and <PERSON><PERSON> Compose
- ✓ Install Node.js 18.x and Yarn
- ✓ Create .env file with secure random secrets
- ✓ Pull and start all containers (PostgreSQL, Meilisearch, Linkwarden)
- ✓ Verify services are running
- ✓ Open the application in your browser

**That's it!** The application will be running at http://localhost:3000

---

### Option 2: Manual Setup

If you prefer to set up manually:

#### 1. Install Prerequisites

```bash
# Install Podman
sudo apt update
sudo apt install -y podman

# Install Podman Compose
sudo apt install -y python3-pip
pip3 install --user podman-compose

# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Install Yarn
sudo npm install -g yarn
```

#### 2. Configure Environment

The `.env` file is already created with secure defaults. You can edit it if needed:

```bash
nano .env
```

#### 3. Start Services

```bash
cd ~/Documents/NORMANS/NORMANS/Bookmark_Manager-main
podman-compose -f podman-compose.yml up -d
```

#### 4. Access Application

Open http://localhost:3000 in your browser

---

## 🎨 Setup VS Code Extensions

To install all recommended VS Code extensions for development:

```bash
cd ~/Documents/NORMANS/NORMANS/Bookmark_Manager-main
./setup-vscode.sh
```

This installs:
- ESLint (linting)
- Prettier (code formatting)
- Prisma (database schema)
- Tailwind CSS IntelliSense
- Playwright Test (E2E testing)
- GitLens (Git integration)
- And more...

---

## 📋 Common Commands

### Service Management

```bash
# Start all services
podman-compose -f podman-compose.yml up -d

# Stop all services
podman-compose -f podman-compose.yml down

# Restart a specific service
podman-compose -f podman-compose.yml restart linkwarden

# View service status
podman-compose -f podman-compose.yml ps

# View logs (all services)
podman-compose -f podman-compose.yml logs -f

# View logs (specific service)
podman-compose -f podman-compose.yml logs -f linkwarden
```

### Development Commands

```bash
# Install dependencies (if running from source)
yarn install

# Start development server (requires local Node.js)
yarn dev

# Build for production
yarn build

# Start production server
yarn start

# Run E2E tests
yarn e2e

# Open Prisma Studio (database GUI)
npx prisma studio
```

### Container Management

```bash
# List running containers
podman ps

# List all containers (including stopped)
podman ps -a

# View container logs
podman logs linkwarden

# Execute command in container
podman exec -it linkwarden sh

# Remove all stopped containers
podman container prune

# Remove all unused images
podman image prune -a
```

---

## 🧪 Testing the Application

### Manual Testing Checklist

1. **Register a User**
   - Go to http://localhost:3000
   - Click "Register" or "Sign Up"
   - Create account with username and password
   - Login with your credentials

2. **Create a Collection**
   - Click "New Collection" or "+"
   - Enter name and description
   - Save collection

3. **Add a Bookmark**
   - Click "Add Link" or "+"
   - Enter a URL (e.g., https://github.com)
   - Select collection
   - Add tags (optional)
   - Save bookmark

4. **Verify Archival**
   - Wait 10-30 seconds for archival to complete
   - Click on the bookmark
   - Check that screenshot, PDF, and readable view are available

5. **Test Search**
   - Use the search bar
   - Search for bookmark title or content
   - Verify results appear

6. **Test Collections**
   - Create multiple collections
   - Move bookmarks between collections
   - Delete a collection

### Automated E2E Testing

```bash
# Make sure application is running at http://localhost:3000
podman-compose -f podman-compose.yml ps

# Install Playwright browsers (first time only)
npx playwright install --with-deps chromium

# Run E2E tests
yarn e2e

# Run tests in UI mode (interactive)
npx playwright test --ui

# Run tests in headed mode (see browser)
npx playwright test --headed
```

---

## 🔧 Troubleshooting

### Application won't start

```bash
# Check if containers are running
podman-compose -f podman-compose.yml ps

# Check logs for errors
podman-compose -f podman-compose.yml logs linkwarden

# Restart services
podman-compose -f podman-compose.yml restart
```

### Port already in use

```bash
# Check what's using port 3000
sudo lsof -i :3000

# Kill the process or change port in podman-compose.yml
```

### Database connection errors

```bash
# Check if PostgreSQL is running
podman ps | grep postgres

# Check PostgreSQL logs
podman logs postgres

# Restart PostgreSQL
podman restart postgres
```

### Meilisearch not working

```bash
# Check if Meilisearch is running
podman ps | grep meilisearch

# Check Meilisearch logs
podman logs meilisearch

# Test Meilisearch health
curl http://localhost:7700/health
```

### Clean slate (reset everything)

```bash
# Stop and remove all containers and volumes
podman-compose -f podman-compose.yml down -v

# Remove data directories
rm -rf pgdata meili_data data

# Start fresh
podman-compose -f podman-compose.yml up -d
```

---

## 📚 Additional Documentation

- **Full Setup Guide**: [SETUP_GUIDE.md](./SETUP_GUIDE.md)
- **Deployment Guide**: [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md)
- **Architecture**: [ARCHITECTURE.md](./ARCHITECTURE.md)
- **Official Docs**: https://docs.linkwarden.app

---

## 🎯 Next Steps

1. ✅ Application running at http://localhost:3000
2. ✅ Register your first user
3. ✅ Create collections and add bookmarks
4. ✅ Explore features (tags, search, archival, sharing)
5. ✅ Run E2E tests to verify everything works
6. ✅ Read deployment guide for production setup

---

## 🆘 Getting Help

- **GitHub Issues**: https://github.com/linkwarden/linkwarden/issues
- **Discord Community**: https://discord.com/invite/CtuYV47nuJ
- **Documentation**: https://docs.linkwarden.app

---

## 🔐 Security Notes

- The `.env` file contains sensitive secrets - keep it secure
- Default configuration is for local development only
- For production deployment, see [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md)
- Change default passwords and secrets before deploying to production
- Enable HTTPS for production deployments

---

**Enjoy using Linkwarden! 🎉**

