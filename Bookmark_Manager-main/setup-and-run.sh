#!/bin/bash

# Linkwarden Setup and Run Script
# This script installs all prerequisites and runs the application with Podman

set -e  # Exit on error

echo "=========================================="
echo "Linkwarden Setup and Run Script"
echo "=========================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ $1${NC}"
}

# Check if running as root
if [ "$EUID" -eq 0 ]; then 
    print_error "Please do not run this script as root"
    exit 1
fi

echo "Step 1: Checking prerequisites..."
echo ""

# Check for sudo access
if ! sudo -n true 2>/dev/null; then
    print_info "This script requires sudo access. You may be prompted for your password."
    sudo -v
fi

# Update package list
print_info "Updating package list..."
sudo apt update -qq

# Check and install Podman
if ! command -v podman &> /dev/null; then
    print_info "Installing Podman..."
    sudo apt install -y podman
    print_success "Podman installed"
else
    print_success "Podman already installed ($(podman --version))"
fi

# Check and install Podman Compose
if ! command -v podman-compose &> /dev/null; then
    print_info "Installing Podman Compose..."
    sudo apt install -y python3-pip
    pip3 install --user podman-compose
    
    # Add to PATH if not already there
    if [[ ":$PATH:" != *":$HOME/.local/bin:"* ]]; then
        echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
        export PATH="$HOME/.local/bin:$PATH"
    fi
    
    print_success "Podman Compose installed"
else
    print_success "Podman Compose already installed ($(podman-compose --version))"
fi

# Check and install Node.js
if ! command -v node &> /dev/null; then
    print_info "Installing Node.js 18.x..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt install -y nodejs
    print_success "Node.js installed ($(node --version))"
else
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_info "Node.js version is too old. Installing Node.js 18.x..."
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt install -y nodejs
        print_success "Node.js upgraded ($(node --version))"
    else
        print_success "Node.js already installed ($(node --version))"
    fi
fi

# Check and install Yarn
if ! command -v yarn &> /dev/null; then
    print_info "Installing Yarn..."
    sudo npm install -g yarn
    print_success "Yarn installed ($(yarn --version))"
else
    print_success "Yarn already installed ($(yarn --version))"
fi

echo ""
echo "Step 2: Setting up Podman..."
echo ""

# Enable Podman socket (for podman-compose)
if ! systemctl --user is-active --quiet podman.socket; then
    print_info "Enabling Podman socket..."
    systemctl --user enable --now podman.socket
    print_success "Podman socket enabled"
else
    print_success "Podman socket already running"
fi

echo ""
echo "Step 3: Checking configuration files..."
echo ""

# Check if .env exists
if [ ! -f .env ]; then
    print_error ".env file not found!"
    print_info "Creating .env file with default values..."
    
    # Generate random secrets
    NEXTAUTH_SECRET=$(openssl rand -base64 32)
    MEILI_MASTER_KEY=$(openssl rand -base64 32)
    POSTGRES_PASSWORD=$(openssl rand -base64 16)
    
    cat > .env << EOF
# Database Configuration
DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@localhost:5432/postgres

# NextAuth Configuration
NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
NEXTAUTH_URL=http://localhost:3000

# Meilisearch Configuration
MEILI_HOST=http://localhost:7700
MEILI_MASTER_KEY=${MEILI_MASTER_KEY}

# Storage Configuration
STORAGE_FOLDER=data

# Application Configuration
NEXT_PUBLIC_DISABLE_REGISTRATION=false
NEXT_PUBLIC_CREDENTIALS_ENABLED=true

# Postgres Password
POSTGRES_PASSWORD=${POSTGRES_PASSWORD}

# Worker Configuration
ARCHIVE_TAKE_COUNT=5
INDEX_TAKE_COUNT=50
ARCHIVE_SCRIPT_INTERVAL=10
NEXT_PUBLIC_RSS_POLLING_INTERVAL_MINUTES=60
EOF
    
    print_success ".env file created with random secrets"
else
    print_success ".env file exists"
fi

# Check if podman-compose.yml exists
if [ ! -f podman-compose.yml ]; then
    print_error "podman-compose.yml not found!"
    exit 1
else
    print_success "podman-compose.yml exists"
fi

echo ""
echo "Step 4: Starting services with Podman Compose..."
echo ""

# Stop any existing containers
print_info "Stopping any existing containers..."
podman-compose -f podman-compose.yml down 2>/dev/null || true

# Pull images
print_info "Pulling container images (this may take a few minutes)..."
podman-compose -f podman-compose.yml pull

# Start services
print_info "Starting services..."
podman-compose -f podman-compose.yml up -d

# Wait for services to be healthy
print_info "Waiting for services to be ready..."
sleep 10

# Check service status
echo ""
print_info "Checking service status..."
podman-compose -f podman-compose.yml ps

echo ""
echo "Step 5: Verifying services..."
echo ""

# Check PostgreSQL
if podman ps | grep -q postgres; then
    print_success "PostgreSQL is running"
else
    print_error "PostgreSQL is not running"
fi

# Check Meilisearch
if podman ps | grep -q meilisearch; then
    print_success "Meilisearch is running"
else
    print_error "Meilisearch is not running"
fi

# Check Linkwarden
if podman ps | grep -q linkwarden; then
    print_success "Linkwarden is running"
else
    print_error "Linkwarden is not running"
fi

# Wait for application to be ready
print_info "Waiting for application to be ready (this may take 30-60 seconds)..."
sleep 30

# Check if application is responding
MAX_RETRIES=10
RETRY_COUNT=0
while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 | grep -q "200\|301\|302"; then
        print_success "Application is responding!"
        break
    else
        RETRY_COUNT=$((RETRY_COUNT + 1))
        if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
            print_info "Waiting for application... (attempt $RETRY_COUNT/$MAX_RETRIES)"
            sleep 5
        else
            print_error "Application is not responding after $MAX_RETRIES attempts"
            print_info "Check logs with: podman-compose -f podman-compose.yml logs linkwarden"
        fi
    fi
done

echo ""
echo "=========================================="
echo "✓ Setup Complete!"
echo "=========================================="
echo ""
echo "Application is running at: http://localhost:3000"
echo ""
echo "Useful commands:"
echo "  - View logs:           podman-compose -f podman-compose.yml logs -f"
echo "  - Stop services:       podman-compose -f podman-compose.yml down"
echo "  - Restart services:    podman-compose -f podman-compose.yml restart"
echo "  - View status:         podman-compose -f podman-compose.yml ps"
echo ""
echo "Next steps:"
echo "  1. Open http://localhost:3000 in your browser"
echo "  2. Register a new user account"
echo "  3. Start adding bookmarks!"
echo ""
echo "To stop the application, run:"
echo "  podman-compose -f podman-compose.yml down"
echo ""

# Ask if user wants to open browser
read -p "Would you like to open the application in your browser now? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if command -v xdg-open &> /dev/null; then
        xdg-open http://localhost:3000
    elif command -v gnome-open &> /dev/null; then
        gnome-open http://localhost:3000
    else
        print_info "Please open http://localhost:3000 in your browser manually"
    fi
fi

