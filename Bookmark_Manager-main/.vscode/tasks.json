{"version": "2.0.0", "tasks": [{"label": "Start Dev Server", "type": "shell", "command": "yarn dev", "problemMatcher": [], "isBackground": true}, {"label": "Build Production", "type": "shell", "command": "yarn build", "problemMatcher": []}, {"label": "Run E2E Tests", "type": "shell", "command": "yarn e2e", "problemMatcher": []}, {"label": "Prisma Studio", "type": "shell", "command": "npx prisma studio", "problemMatcher": []}, {"label": "Start Podman Services", "type": "shell", "command": "podman-compose -f podman-compose.yml up -d", "problemMatcher": []}, {"label": "Stop Podman Services", "type": "shell", "command": "podman-compose -f podman-compose.yml down", "problemMatcher": []}]}