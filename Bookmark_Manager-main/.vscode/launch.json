{"version": "0.2.0", "configurations": [{"name": "Next.js: debug server-side", "type": "node-terminal", "request": "launch", "command": "yarn dev"}, {"name": "Next.js: debug client-side", "type": "chrome", "request": "launch", "url": "http://localhost:3000"}, {"name": "Next.js: debug full stack", "type": "node-terminal", "request": "launch", "command": "yarn dev", "serverReadyAction": {"pattern": "started server on .+, url: (https?://.+)", "uriFormat": "%s", "action": "debugWithChrome"}}, {"name": "Playwright: Debug Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/playwright", "args": ["test", "--debug"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}]}