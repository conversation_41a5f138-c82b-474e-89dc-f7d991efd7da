@tailwind base;
@tailwind components;
@tailwind utilities;

[data-theme="dark"] {
  color-scheme: dark;
  --default: 82.76% 0.101277 230.317893;
  --red: 69.73% 0.168 22.43;
  --rose: 75% 0.2 340;
  --yellow: 90% 0.1 90;
  --green: 88.58% 0.2465 140;
  --orange: 80% 0.18 60;
  --zinc: 88.45% 0.05 220;
}

[data-theme="light"] {
  color-scheme: light;
  --default: 50% 0.1193 242.75;
  --red: 49.7% 0.2026 22.43;
  --rose: 55% 0.25 340;
  --yellow: 69.58% 0.2 90;
  --green: 60% 0.2 140;
  --orange: 70% 0.22 60;
  --zinc: 50% 0.07 220;
}

/* Hide scrollbar */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}
.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hide-color-picker {
  opacity: 0;
  display: block;
  width: 32px;
  height: 32px;
  border: none;
}

.hyphens {
  hyphens: auto;
}

.rotate {
  transition: transform 0.1s ease;
  transform: rotate(-90deg);
}

.rotate-reverse {
  transition: transform 0.1s ease;
  transform: rotate(0deg);
}

.fade-in {
  animation: fade-in-animation 200ms;
}

@keyframes fade-in-animation {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.slide-up {
  animation: slide-up-animation 200ms;
}

.slide-down {
  animation: slide-down-animation 70ms;
}

@keyframes slide-down-animation {
  0% {
    transform: translateY(-15%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-up-animation {
  0% {
    transform: translateY(5%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-right {
  animation: slide-right-animation 200ms;
}

@keyframes slide-right-animation {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(0);
  }
}

.slide-right-with-fade {
  animation: slide-right-animation 150ms;
}

@keyframes slide-right-animation {
  0% {
    transform: translateX(-50%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.spin {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* For react-colorful */
.color-picker .react-colorful {
  height: 7rem;
  width: 7rem;
}
.color-picker .react-colorful__hue {
  height: 1rem;
}
.color-picker .react-colorful__pointer {
  width: 1rem;
  height: 1rem;
  border-width: 1px;
}

.react-select__indicator-separator {
  display: none;
}

.react-select__control--is-focused .react-select__dropdown-indicator,
.react-select__control .react-select__dropdown-indicator,
.react-select__control .react-select__dropdown-indicator:hover,
.react-select__control .react-select__dropdown-indicator:focus,
.react-select__control--is-focused .react-select__dropdown-indicator:hover,
.react-select__control--is-focused .react-select__dropdown-indicator:focus {
  color: oklch(var(--n));
}

/* Theme */

@layer components {
  .react-select-container .react-select__control {
    @apply bg-base-200 hover:border-neutral-content;
  }

  .react-select-container .react-select__control--is-focused {
    @apply border-primary hover:border-primary;
  }

  .react-select-container .react-select__menu {
    @apply bg-base-100 border-neutral-content border rounded-md;
  }

  .react-select-container .react-select__input-container,
  .react-select-container .react-select__single-value {
    @apply text-base-content;
  }
}

.line-break * {
  overflow-y: hidden;
}

.custom-file-input::file-selector-button {
  cursor: pointer;
}

/* Reader view custom stylings */
.reader-view {
  /* line-height: 2.8rem; */
}
.reader-view * {
  text-align: inherit;
}
.reader-view mark {
  background-color: oklch(var(--p) / 0.5);
  color: oklch(var(--bc));
  border-radius: 5px;
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.reader-view p {
  font-size: 1.15rem;
  line-height: 1.8;
  margin-bottom: 1rem;
  margin-top: 1rem;
}
.reader-view h1 {
  font-size: 2.2rem;
  line-height: 1.3;
}
.reader-view h2 {
  font-size: 1.9rem;
}
.reader-view h3 {
  font-size: 1.6rem;
}
.reader-view h4 {
  font-size: 1.3rem;
}
.reader-view h5 {
  font-size: 1.1rem;
}
.reader-view li {
  list-style: inside;
  margin-left: 2rem;
}
.reader-view a {
  text-decoration: underline;
}
.reader-view b {
  font-weight: bolder;
}
.reader-view pre,
.reader-view code {
  font-family: "Courier New", Courier, monospace;
}
.reader-view blockquote {
  opacity: 50%;
  padding-left: 1.5rem;
  border-left: solid 3px;
}
.reader-view img {
  margin: auto;
  margin-top: 2rem;
  margin-bottom: 2rem;
  border-radius: 10px;
}
.reader-view pre {
  padding: 1rem;
  line-height: normal;
}
.reader-view code {
  padding: 0.15rem 0.4rem 0.15rem 0.4rem;
}
[data-theme="dark"] .reader-view code,
[data-theme="dark"] .reader-view pre {
  background-color: rgb(49, 49, 49);
  border-radius: 8px;
}
[data-theme="light"] .reader-view code,
[data-theme="light"] .reader-view pre {
  background-color: rgb(230, 230, 230);
  border-radius: 8px;
}

.reader-view ul[data-type="taskList"] {
  list-style: none;
  margin-left: 0;
  padding: 0;
}

.reader-view ul[data-type="taskList"] li {
  align-items: center;
  display: flex;
}

.reader-view ul[data-type="taskList"] li > label {
  flex: 0 0 auto;
  margin-right: 0.5rem;
  user-select: none;
}

.reader-view ul[data-type="taskList"] li > div {
  flex: 1 1 auto;
}

.reader-view ul[data-type="taskList"] input[type="checkbox"] {
  cursor: pointer;
}

.read-only ul[data-type="taskList"] {
  pointer-events: none;
}
