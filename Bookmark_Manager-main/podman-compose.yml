services:
  postgres:
    image: docker.io/library/postgres:16-alpine
    env_file: .env
    restart: always
    ports:
      - "5432:5432"
    volumes:
      - ./pgdata:/var/lib/postgresql/data
    environment:
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_USER=postgres
      - POSTGRES_DB=postgres
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  meilisearch:
    image: docker.io/getmeili/meilisearch:v1.12.8
    restart: always
    env_file:
      - .env
    ports:
      - "7700:7700"
    volumes:
      - ./meili_data:/meili_data
    environment:
      - MEILI_MASTER_KEY=${MEILI_MASTER_KEY}
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--spider", "http://localhost:7700/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  linkwarden:
    env_file: .env
    environment:
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/postgres
      - MEILI_HOST=http://meilisearch:7700
      - MEILI_MASTER_KEY=${MEILI_MASTER_KEY}
    restart: always
    # Uncomment the next line and comment the image line to build from source
    # build: .
    image: ghcr.io/linkwarden/linkwarden:latest
    ports:
      - "3000:3000"
    volumes:
      - ./data:/data/data
    depends_on:
      postgres:
        condition: service_healthy
      meilisearch:
        condition: service_healthy

