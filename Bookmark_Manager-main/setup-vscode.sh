#!/bin/bash

# VS Code Extensions Setup Script for Linkwarden Development
# This script installs all recommended VS Code extensions

echo "=========================================="
echo "Installing VS Code Extensions for Linkwarden"
echo "=========================================="
echo ""

# Check if code command is available
if ! command -v code &> /dev/null; then
    echo "❌ VS Code 'code' command not found in PATH"
    echo "Please install VS Code and ensure 'code' command is available"
    echo "You can add it via: Command Palette > Shell Command: Install 'code' command in PATH"
    exit 1
fi

echo "✓ VS Code found"
echo ""

# Array of extensions to install
extensions=(
    "dbaeumer.vscode-eslint"                    # ESLint
    "esbenp.prettier-vscode"                    # Prettier
    "Prisma.prisma"                             # Prisma
    "bradlc.vscode-tailwindcss"                 # Tailwind CSS IntelliSense
    "ms-vscode-remote.remote-containers"        # Dev Containers (Podman/Docker)
    "eamodio.gitlens"                           # GitLens
    "christian-kohler.path-intellisense"        # Path Intellisense
    "formulahendry.auto-rename-tag"             # Auto Rename Tag
    "ms-playwright.playwright"                  # Playwright Test
    "usernamehw.errorlens"                      # Error Lens
    "dsznajder.es7-react-js-snippets"          # React snippets
    "dbaeumer.vscode-eslint"                    # ESLint
    "yoavbls.pretty-ts-errors"                  # Pretty TypeScript Errors
    "streetsidesoftware.code-spell-checker"     # Code Spell Checker
)

# Install each extension
installed_count=0
failed_count=0

for extension in "${extensions[@]}"; do
    echo "Installing: $extension"
    if code --install-extension "$extension" --force > /dev/null 2>&1; then
        echo "  ✓ Installed successfully"
        ((installed_count++))
    else
        echo "  ❌ Failed to install"
        ((failed_count++))
    fi
    echo ""
done

echo "=========================================="
echo "Installation Summary"
echo "=========================================="
echo "✓ Successfully installed: $installed_count extensions"
if [ $failed_count -gt 0 ]; then
    echo "❌ Failed to install: $failed_count extensions"
fi
echo ""

# Create VS Code settings for the workspace
echo "Creating workspace settings..."

mkdir -p .vscode

cat > .vscode/settings.json << 'EOF'
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.enablePromptUseWorkspaceTsdk": true,
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],
  "files.associations": {
    "*.css": "tailwindcss"
  },
  "prisma.showPrismaDataPlatformNotification": false,
  "[prisma]": {
    "editor.defaultFormatter": "Prisma.prisma"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "files.exclude": {
    "**/.git": true,
    "**/.DS_Store": true,
    "**/node_modules": true,
    "**/.next": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/bower_components": true,
    "**/.next": true,
    "**/dist": true,
    "**/build": true
  }
}
EOF

echo "✓ Created .vscode/settings.json"

# Create recommended extensions file
cat > .vscode/extensions.json << 'EOF'
{
  "recommendations": [
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "Prisma.prisma",
    "bradlc.vscode-tailwindcss",
    "ms-vscode-remote.remote-containers",
    "eamodio.gitlens",
    "christian-kohler.path-intellisense",
    "formulahendry.auto-rename-tag",
    "ms-playwright.playwright",
    "usernamehw.errorlens",
    "dsznajder.es7-react-js-snippets",
    "yoavbls.pretty-ts-errors",
    "streetsidesoftware.code-spell-checker"
  ]
}
EOF

echo "✓ Created .vscode/extensions.json"

# Create launch configuration for debugging
cat > .vscode/launch.json << 'EOF'
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Next.js: debug server-side",
      "type": "node-terminal",
      "request": "launch",
      "command": "yarn dev"
    },
    {
      "name": "Next.js: debug client-side",
      "type": "chrome",
      "request": "launch",
      "url": "http://localhost:3000"
    },
    {
      "name": "Next.js: debug full stack",
      "type": "node-terminal",
      "request": "launch",
      "command": "yarn dev",
      "serverReadyAction": {
        "pattern": "started server on .+, url: (https?://.+)",
        "uriFormat": "%s",
        "action": "debugWithChrome"
      }
    },
    {
      "name": "Playwright: Debug Tests",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/.bin/playwright",
      "args": ["test", "--debug"],
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    }
  ]
}
EOF

echo "✓ Created .vscode/launch.json"

# Create tasks configuration
cat > .vscode/tasks.json << 'EOF'
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Start Dev Server",
      "type": "shell",
      "command": "yarn dev",
      "problemMatcher": [],
      "isBackground": true
    },
    {
      "label": "Build Production",
      "type": "shell",
      "command": "yarn build",
      "problemMatcher": []
    },
    {
      "label": "Run E2E Tests",
      "type": "shell",
      "command": "yarn e2e",
      "problemMatcher": []
    },
    {
      "label": "Prisma Studio",
      "type": "shell",
      "command": "npx prisma studio",
      "problemMatcher": []
    },
    {
      "label": "Start Podman Services",
      "type": "shell",
      "command": "podman-compose -f podman-compose.yml up -d",
      "problemMatcher": []
    },
    {
      "label": "Stop Podman Services",
      "type": "shell",
      "command": "podman-compose -f podman-compose.yml down",
      "problemMatcher": []
    }
  ]
}
EOF

echo "✓ Created .vscode/tasks.json"

echo ""
echo "=========================================="
echo "✓ VS Code Setup Complete!"
echo "=========================================="
echo ""
echo "Next steps:"
echo "1. Restart VS Code to activate all extensions"
echo "2. Open the project folder in VS Code"
echo "3. Extensions will be automatically recommended"
echo "4. Use Command Palette (Ctrl+Shift+P) to access features"
echo ""
echo "Useful VS Code shortcuts:"
echo "  - Ctrl+Shift+P: Command Palette"
echo "  - Ctrl+`: Toggle Terminal"
echo "  - Ctrl+Shift+B: Run Build Task"
echo "  - F5: Start Debugging"
echo ""

