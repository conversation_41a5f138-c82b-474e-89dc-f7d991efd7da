# <PERSON><PERSON>en (Bookmark Manager) - Complete Setup Guide

## Table of Contents
1. [Prerequisites Installation](#prerequisites-installation)
2. [VS Code Extensions](#vs-code-extensions)
3. [Running with <PERSON><PERSON>](#running-with-podman)
4. [Running from Source (Development)](#running-from-source-development)
5. [Testing the Application](#testing-the-application)
6. [Troubleshooting](#troubleshooting)
7. [Deployment to Debian Cloud Instance](#deployment-to-debian-cloud-instance)

---

## Prerequisites Installation

### 1. Install <PERSON> and <PERSON><PERSON>-Compose

```bash
# Update package list
sudo apt update

# Install Podman
sudo apt install -y podman

# Install Podman-Compose (Python-based)
sudo apt install -y python3-pip
pip3 install podman-compose

# Verify installation
podman --version
podman-compose --version
```

### 2. Install Node.js and Yarn

```bash
# Install Node.js 18.x (LTS)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Install Yarn
npm install -g yarn

# Verify installation
node --version
npm --version
yarn --version
```

### 3. Install Playwright Dependencies (for E2E testing)

```bash
# Install Playwright browsers and system dependencies
npx playwright install --with-deps chromium
```

---

## VS Code Extensions

Install these extensions in VS Code for optimal development experience:

### Essential Extensions:
1. **ESLint** (`dbaeumer.vscode-eslint`)
   - JavaScript/TypeScript linting

2. **Prettier - Code formatter** (`esbenp.prettier-vscode`)
   - Code formatting

3. **Prisma** (`Prisma.prisma`)
   - Prisma schema support and syntax highlighting

4. **TypeScript and JavaScript Language Features** (built-in)
   - Enhanced TypeScript support

5. **Tailwind CSS IntelliSense** (`bradlc.vscode-tailwindcss`)
   - Tailwind CSS autocomplete and syntax highlighting

6. **Podman** (`ms-vscode-remote.remote-containers`)
   - Podman/Docker container management

7. **GitLens** (`eamodio.gitlens`)
   - Enhanced Git capabilities

8. **Path Intellisense** (`christian-kohler.path-intellisense`)
   - Autocomplete for file paths

9. **Auto Rename Tag** (`formulahendry.auto-rename-tag`)
   - Auto rename paired HTML/JSX tags

10. **Playwright Test for VSCode** (`ms-playwright.playwright`)
    - Run and debug Playwright tests

### Install via Command Line:
```bash
code --install-extension dbaeumer.vscode-eslint
code --install-extension esbenp.prettier-vscode
code --install-extension Prisma.prisma
code --install-extension bradlc.vscode-tailwindcss
code --install-extension ms-vscode-remote.remote-containers
code --install-extension eamodio.gitlens
code --install-extension christian-kohler.path-intellisense
code --install-extension formulahendry.auto-rename-tag
code --install-extension ms-playwright.playwright
```

---

## Running with Podman

### Quick Start (Recommended for Testing)

1. **Navigate to project directory:**
```bash
cd ~/Documents/NORMANS/NORMANS/Bookmark_Manager-main
```

2. **Start all services with Podman Compose:**
```bash
podman-compose -f podman-compose.yml up -d
```

3. **Check service status:**
```bash
podman-compose -f podman-compose.yml ps
```

4. **View logs:**
```bash
# All services
podman-compose -f podman-compose.yml logs -f

# Specific service
podman-compose -f podman-compose.yml logs -f linkwarden
```

5. **Access the application:**
   - Open browser: http://localhost:3000
   - Register a new user
   - Start using the bookmark manager!

6. **Stop services:**
```bash
podman-compose -f podman-compose.yml down
```

7. **Stop and remove volumes (clean slate):**
```bash
podman-compose -f podman-compose.yml down -v
rm -rf pgdata meili_data data
```

### Service Details:
- **PostgreSQL**: Port 5432 (database)
- **Meilisearch**: Port 7700 (search engine)
- **Linkwarden**: Port 3000 (web application)

---

## Running from Source (Development)

### 1. Install Dependencies

```bash
cd ~/Documents/NORMANS/NORMANS/Bookmark_Manager-main
yarn install
```

### 2. Start Database Services Only

```bash
# Start only PostgreSQL and Meilisearch
podman run -d --name postgres \
  -e POSTGRES_PASSWORD=linkwarden_password \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_DB=postgres \
  -p 5432:5432 \
  -v ./pgdata:/var/lib/postgresql/data \
  docker.io/library/postgres:16-alpine

podman run -d --name meilisearch \
  -e MEILI_MASTER_KEY=linkwarden_meili_master_key \
  -p 7700:7700 \
  -v ./meili_data:/meili_data \
  docker.io/getmeili/meilisearch:v1.12.8
```

### 3. Setup Database

```bash
# Run Prisma migrations
npx prisma migrate deploy

# Optional: Seed database
npx prisma db seed
```

### 4. Start Development Server

```bash
# This starts both Next.js and the background worker
yarn dev
```

The application will be available at http://localhost:3000

### 5. Production Build

```bash
# Build the application
yarn build

# Start production server
yarn start
```

---

## Testing the Application

### Manual Testing

1. **Register a User:**
   - Navigate to http://localhost:3000
   - Click "Register" or "Sign Up"
   - Create an account with username and password

2. **Create a Collection:**
   - After login, create a new collection
   - Add a name and description

3. **Add Bookmarks:**
   - Add a URL to your collection
   - The system will automatically:
     - Capture a screenshot
     - Generate a PDF
     - Create a readable version
     - Extract metadata

4. **Test Search:**
   - Use the search bar to find bookmarks
   - Filter by tags, collections

5. **Test Archival Features:**
   - Check preserved formats (PDF, Screenshot, Readable)
   - Verify links are accessible even if original is down

### Automated E2E Testing

```bash
# Make sure the app is running at http://localhost:3000
# Then run Playwright tests
yarn e2e

# Run tests in UI mode (interactive)
npx playwright test --ui

# Run specific test file
npx playwright test e2e/tests/dashboard/links.spec.ts
```

---

## Troubleshooting

### Issue: Podman permission denied
```bash
# Add user to podman group
sudo usermod -aG podman $USER
newgrp podman
```

### Issue: Port already in use
```bash
# Check what's using the port
sudo lsof -i :3000
sudo lsof -i :5432
sudo lsof -i :7700

# Kill the process or change ports in .env
```

### Issue: Database connection failed
```bash
# Check if PostgreSQL is running
podman ps | grep postgres

# Check logs
podman logs postgres

# Restart PostgreSQL
podman restart postgres
```

### Issue: Meilisearch not working
```bash
# Check if Meilisearch is running
podman ps | grep meilisearch

# Check logs
podman logs meilisearch

# Verify connection
curl http://localhost:7700/health
```

### Issue: Prisma migration errors
```bash
# Reset database (WARNING: deletes all data)
npx prisma migrate reset

# Or manually apply migrations
npx prisma migrate deploy
```

### Issue: Node modules errors
```bash
# Clear cache and reinstall
rm -rf node_modules yarn.lock
yarn install
```

---

## Deployment to Debian Cloud Instance

See [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md) for detailed cloud deployment instructions.

Quick summary:
1. Install prerequisites (Node.js, Podman, PostgreSQL)
2. Clone repository
3. Configure environment variables
4. Setup systemd services
5. Configure reverse proxy (Nginx)
6. Setup SSL certificates (Let's Encrypt)
7. Configure firewall

---

## Additional Resources

- **Official Documentation**: https://docs.linkwarden.app
- **GitHub Repository**: https://github.com/linkwarden/linkwarden
- **Discord Community**: https://discord.com/invite/CtuYV47nuJ
- **Demo Instance**: https://demo.linkwarden.app

---

## Quick Reference Commands

```bash
# Start services
podman-compose -f podman-compose.yml up -d

# Stop services
podman-compose -f podman-compose.yml down

# View logs
podman-compose -f podman-compose.yml logs -f

# Restart a service
podman-compose -f podman-compose.yml restart linkwarden

# Check service status
podman-compose -f podman-compose.yml ps

# Development mode
yarn dev

# Production build
yarn build && yarn start

# Run tests
yarn e2e

# Database operations
npx prisma migrate deploy
npx prisma studio  # Open Prisma Studio (database GUI)
```

